"use client";

import { Badge } from "@workspace/ui/components/badge";
import { Check, Clock, Circle, X } from "lucide-react";
import { useForm, revalidateLogic } from "@tanstack/react-form";
import { ApprovalButton } from "@workspace/ui/components/approval.button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { InputValidated } from "@workspace/ui/components/input-validated";
import { Label } from "@workspace/ui/components/label";
import { PlaceholderUser } from "@workspace/ui/components/icons/placeholder-user";
import { useState } from "react";
import { z } from "zod";

// Zod schema for KYC form validation
const kycSchema = z.object({
  firstName: z
    .string()
    .min(1, "Ad gereklidir")
    .min(2, "Ad en az 2 karakter olmalıdır")
    .trim(),
  lastName: z
    .string()
    .min(1, "Soyad gereklidir")
    .min(2, "Soyad en az 2 karakter olmalıdır")
    .trim(),
});

export interface KYCDialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSubmit?: (data: KYCFormData) => void;
  onCancel?: () => void;
  status?: KYCStatus; // Provided by server/admin
}

export type KYCFormData = z.infer<typeof kycSchema>;

export function KYCDialog({
  open = true,
  onOpenChange,
  onSubmit,
  status = "not_submitted",
}: KYCDialogProps) {
  const [isApprovalDialogOpen, setIsApprovalDialogOpen] = useState(false);
  const [isActuallySubmitting, setIsActuallySubmitting] = useState(false);

  const form = useForm({
    defaultValues: {
      firstName: "",
      lastName: "",
    },
    validationLogic: revalidateLogic({
      mode: "submit",
      modeAfterSubmission: "change",
    }),
    validators: {
      onDynamic: kycSchema,
    },
    onSubmit: async ({ value }) => {
      setIsActuallySubmitting(true);
      try {
        onSubmit?.(value);
        await new Promise((r) => setTimeout(r, 3000));
      } finally {
        setIsActuallySubmitting(false);
      }
    },
  });

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <form
        onSubmit={(e) => {
          e.preventDefault();
          e.stopPropagation();
        }}
      >
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{"Kimlik Doğrulama (KYC)"}</DialogTitle>
            <DialogDescription>
              {`Bu bilgiler finansal işlemlerinizde kimlik doğrulama ve güvenlik protokollerimiz kapsamında kullanılacaktır. Lütfen kimlik kartınızda yazan ad soyadınızı doğru bir şekilde girin.`}
            </DialogDescription>
          </DialogHeader>

          {/* ID Card Container */}
          <div className="mt-1 mx-auto p-7 pb-4 max-w-[400px] rounded-xl bg-muted border-2 border-muted-foreground shadow-md">
            {/* ID Card Header with Chip */}

            {/* ID Card Content with Photo and Fields */}
            <div className="flex gap-8">
              {/* Photo Rectangle */}
              <div className="w-26 h-36 grid-background-avatar rounded-md flex items-center justify-center flex-shrink-0 relative overflow-hidden border-4 border-input bg-right">
                <PlaceholderUser className="p-1 h-full w-full fill-input stroke-muted stroke-[20px]" />
              </div>

              {/* Form Fields - Vertical Layout */}
              <div className="flex-1 space-y-4">
                <form.Field name="firstName">
                  {(field) => (
                    <div className="grid gap-2">
                      <Label htmlFor={field.name}>{"Ad"}</Label>
                      <InputValidated
                        id={field.name}
                        name={field.name}
                        disabled={isActuallySubmitting || isApprovalDialogOpen}
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        onBlur={field.handleBlur}
                        required
                        status={
                          field.state.meta.isTouched &&
                          field.state.meta.errors.length > 0
                            ? "error"
                            : undefined
                        }
                        message={
                          field.state.meta.isTouched &&
                          field.state.meta.errors.length > 0
                            ? String(field.state.meta.errors[0]?.message)
                            : undefined
                        }
                      />
                    </div>
                  )}
                </form.Field>

                <form.Field name="lastName">
                  {(field) => (
                    <div className="grid gap-2">
                      <Label htmlFor={field.name}>{"Soyad"}</Label>
                      <InputValidated
                        id={field.name}
                        name={field.name}
                        disabled={isActuallySubmitting || isApprovalDialogOpen}
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        onBlur={field.handleBlur}
                        required
                        status={
                          field.state.meta.isTouched &&
                          field.state.meta.errors.length > 0
                            ? "error"
                            : undefined
                        }
                        message={
                          field.state.meta.isTouched &&
                          field.state.meta.errors.length > 0
                            ? String(field.state.meta.errors[0]?.message)
                            : undefined
                        }
                      />
                    </div>
                  )}
                </form.Field>
              </div>
            </div>

            {/* KYC Status with Chip */}
            <div className="mt-3 flex items-end justify-between gap-3">
              {/* Status Pill */}
              <KYCStatusBadge status={status} />

              {/* Mini Chip Shape */}
              <div className="w-12 h-10 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-sm border border-yellow-700 shadow-sm relative saturate-25">
                <div className="absolute inset-[3px] ">
                  <div className="grid grid-cols-3 gap-px h-full p-0.5">
                    {Array.from({ length: 6 }).map((_, i) => (
                      <div
                        key={i}
                        className="bg-yellow-600/35 rounded-[0.5px]"
                      />
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter className="mt-6">
            <form.Subscribe
              selector={(state) => [
                state.canSubmit,
                state.isSubmitted,
                state.isDirty,
                !!state.values.firstName?.trim(),
                !!state.values.lastName?.trim(),
              ]}
            >
              {([canSubmit, isSubmitted, isDirty]) => (
                <ApprovalButton
                  disclaimer="Bilgileriniz değerlendirme sürecine alınacaktır. Onaylıyor musunuz?"
                  variant="primary"
                  disabled={!canSubmit || isActuallySubmitting}
                  needApproval={canSubmit && !isSubmitted && isDirty}
                  onOpenChange={(open) => setIsApprovalDialogOpen(open)}
                  onApprove={async () => {
                    await form.handleSubmit();
                  }}
                >
                  {"GÖNDER"}
                </ApprovalButton>
              )}
            </form.Subscribe>
          </DialogFooter>
        </DialogContent>
      </form>
    </Dialog>
  );
}
export type KYCStatus = "not_submitted" | "submitted" | "rejected" | "pending";

export interface KYCStatusBadgeProps {
  status: KYCStatus;
  className?: string;
}

export function KYCStatusBadge({ status, className }: KYCStatusBadgeProps) {
  switch (status) {
    case "submitted":
      return (
        <Badge className={className}>
          <Check /> KYC Gönderildi
        </Badge>
      );
    case "rejected":
      return (
        <Badge variant="destructive" className={className}>
          <X /> KYC Reddedildi
        </Badge>
      );
    case "pending":
      return (
        <Badge variant="secondary" className={className}>
          <Clock />
        </Badge>
      );
    case "not_submitted":
    default:
      return (
        <Badge variant="outline" className={className}>
          <Circle />
          {"Henüz Gönderilmedi"}
        </Badge>
      );
  }
}
