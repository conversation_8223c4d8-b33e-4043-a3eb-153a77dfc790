import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@workspace/ui/components/tooltip";

export function TooltipNotification({
  children,
  message,
  open,
}: {
  children: React.ReactNode;
  message: string;
  open?: boolean;
}) {
  return (
    <Tooltip open={open}>
      <TooltipTrigger asChild>{children}</TooltipTrigger>
      <TooltipContent
        align="end"
        className="bg-destructive rounded-xs -skew-x-12"
        arrowClassName="fill-destructive bg-destructive"
      >
        <span className="skew-x-12">{message}</span>
      </TooltipContent>
    </Tooltip>
  );
}
